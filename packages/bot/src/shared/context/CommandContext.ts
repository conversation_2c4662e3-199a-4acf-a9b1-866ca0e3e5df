/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Improved Command Context Architecture
 * 
 * This file provides a cleaner, more maintainable approach to command contexts
 * that eliminates the need for UnifiedContext adapters while maintaining
 * type safety and supporting both slash commands and prefix commands.
 */

import type { 
  User, 
  Guild, 
  GuildMember, 
  Message, 
  ChatInputCommandInteraction,
  InteractionResponse,
  Channel,
  Role,
  Attachment
} from 'discord.js';
import type { Context } from './Context.js';
import type { InteractionContext } from './InteractionContext.js';
import type { PrefixContext } from './PrefixContext.js';

/**
 * Union type for all supported context types
 * This replaces the UnifiedContext interface with a cleaner union approach
 */
export type CommandContext = InteractionContext | PrefixContext;

/**
 * Type guard to check if context is an InteractionContext
 */
export function isInteractionContext(ctx: CommandContext): ctx is InteractionContext {
  return 'interaction' in ctx && !('args' in ctx);
}

/**
 * Type guard to check if context is a PrefixContext
 */
export function isPrefixContext(ctx: CommandContext): ctx is PrefixContext {
  return 'args' in ctx;
}

/**
 * Utility functions for working with CommandContext
 */
export class CommandContextUtils {
  /**
   * Get user ID from any context type - solves the cooldown system issue
   */
  static getUserId(ctx: CommandContext): string {
    return ctx.userId;
  }

  /**
   * Get user from any context type
   */
  static getUser(ctx: CommandContext): User {
    return ctx.user;
  }

  /**
   * Get guild from any context type
   */
  static getGuild(ctx: CommandContext): Guild | null {
    return ctx.guild;
  }

  /**
   * Get member from any context type
   */
  static getMember(ctx: CommandContext): GuildMember | null {
    return ctx.member;
  }

  /**
   * Get channel from any context type
   */
  static getChannel(ctx: CommandContext): Channel | null {
    return ctx.channel;
  }

  /**
   * Check if context is in a guild
   */
  static inGuild(ctx: CommandContext): boolean {
    return ctx.inGuild();
  }

  /**
   * Get string option/argument with type safety
   */
  static getString(ctx: CommandContext, nameOrIndex: string | number): string | null {
    if (isInteractionContext(ctx)) {
      if (typeof nameOrIndex === 'string') {
        return ctx.options.getString(nameOrIndex);
      }
      throw new Error('InteractionContext requires string option names');
    } else {
      if (typeof nameOrIndex === 'number') {
        return ctx.getString(nameOrIndex);
      }
      throw new Error('PrefixContext requires numeric argument indices');
    }
  }

  /**
   * Get integer option/argument with type safety
   */
  static getInteger(ctx: CommandContext, nameOrIndex: string | number): number | null {
    if (isInteractionContext(ctx)) {
      if (typeof nameOrIndex === 'string') {
        return ctx.options.getInteger(nameOrIndex);
      }
      throw new Error('InteractionContext requires string option names');
    } else {
      if (typeof nameOrIndex === 'number') {
        return ctx.getInteger(nameOrIndex);
      }
      throw new Error('PrefixContext requires numeric argument indices');
    }
  }

  /**
   * Get number option/argument with type safety
   */
  static getNumber(ctx: CommandContext, nameOrIndex: string | number): number | null {
    if (isInteractionContext(ctx)) {
      if (typeof nameOrIndex === 'string') {
        return ctx.options.getNumber(nameOrIndex);
      }
      throw new Error('InteractionContext requires string option names');
    } else {
      if (typeof nameOrIndex === 'number') {
        return ctx.getNumber(nameOrIndex);
      }
      throw new Error('PrefixContext requires numeric argument indices');
    }
  }

  /**
   * Get boolean option/argument with type safety
   */
  static getBoolean(ctx: CommandContext, nameOrIndex: string | number): boolean | null {
    if (isInteractionContext(ctx)) {
      if (typeof nameOrIndex === 'string') {
        return ctx.options.getBoolean(nameOrIndex);
      }
      throw new Error('InteractionContext requires string option names');
    } else {
      if (typeof nameOrIndex === 'number') {
        return ctx.getBoolean(nameOrIndex);
      }
      throw new Error('PrefixContext requires numeric argument indices');
    }
  }

  /**
   * Get user option/argument with type safety
   */
  static async getUser(ctx: CommandContext, nameOrIndex: string | number): Promise<User | null> {
    if (isInteractionContext(ctx)) {
      if (typeof nameOrIndex === 'string') {
        return await ctx.options.getUser(nameOrIndex);
      }
      throw new Error('InteractionContext requires string option names');
    } else {
      if (typeof nameOrIndex === 'number') {
        return await ctx.getUser(nameOrIndex);
      }
      throw new Error('PrefixContext requires numeric argument indices');
    }
  }

  /**
   * Get channel option/argument with type safety
   */
  static getChannel(ctx: CommandContext, nameOrIndex: string | number): Channel | null {
    if (isInteractionContext(ctx)) {
      if (typeof nameOrIndex === 'string') {
        return ctx.options.getChannel(nameOrIndex);
      }
      throw new Error('InteractionContext requires string option names');
    } else {
      if (typeof nameOrIndex === 'number') {
        return ctx.getChannel(nameOrIndex);
      }
      throw new Error('PrefixContext requires numeric argument indices');
    }
  }

  /**
   * Get role option/argument with type safety
   */
  static getRole(ctx: CommandContext, nameOrIndex: string | number): Role | null {
    if (isInteractionContext(ctx)) {
      if (typeof nameOrIndex === 'string') {
        return ctx.options.getRole(nameOrIndex);
      }
      throw new Error('InteractionContext requires string option names');
    } else {
      if (typeof nameOrIndex === 'number') {
        return ctx.getRole(nameOrIndex);
      }
      throw new Error('PrefixContext requires numeric argument indices');
    }
  }

  /**
   * Get subcommand with type safety
   */
  static getSubcommand(ctx: CommandContext): string | null {
    if (isInteractionContext(ctx)) {
      return ctx.options.getSubcommand();
    } else {
      return ctx.getSubcommand();
    }
  }

  /**
   * Reply to context with unified interface
   */
  static async reply(ctx: CommandContext, options: any): Promise<Message | InteractionResponse> {
    return await ctx.reply(options);
  }

  /**
   * Edit reply with unified interface
   */
  static async editReply(ctx: CommandContext, options: any): Promise<Message | InteractionResponse> {
    return await ctx.editReply(options);
  }

  /**
   * Follow up with unified interface
   */
  static async followUp(ctx: CommandContext, options: any): Promise<Message> {
    return await ctx.followUp(options);
  }

  /**
   * Defer reply with unified interface
   */
  static async deferReply(ctx: CommandContext, options?: any): Promise<any> {
    return await ctx.deferReply(options);
  }
}

/**
 * Enhanced base command handler interface that works with the new context system
 */
export interface EnhancedCommandHandler {
  /**
   * Execute command with proper context typing
   * Commands can use type guards to handle different context types appropriately
   */
  execute(ctx: CommandContext): Promise<void | any>;
}
