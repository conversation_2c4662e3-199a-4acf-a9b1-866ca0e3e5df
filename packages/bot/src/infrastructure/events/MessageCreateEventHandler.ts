/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Modern MessageCreate Event Handler
 *
 * Handles message events and routes prefix commands through the flexible command architecture.
 * Integrates with the new command system while maintaining backward compatibility.
 */

import { injectable, inject } from 'inversify';
import type { Message } from 'discord.js';
import { TYPES } from '../../shared/types/TYPES.js';
import type { CommandRegistry } from '../../presentation/commands/CommandRegistry.js';
import { Logger } from '../../shared/utils/Logger.js';
import type { IDiscordEventHandler } from '../loaders/DynamicEventLoader.js';

/**
 * Modern MessageCreate Event Handler
 *
 * Routes prefix commands through the flexible command architecture.
 */
@injectable()
export class MessageCreateEventHandler implements IDiscordEventHandler {
  readonly eventName = 'messageCreate';
  readonly once = false;

  constructor(
    @inject(TYPES.CommandRegistry) private readonly commandRegistry: CommandRegistry,
  ) {}

  /**
   * Handle Discord message events
   */
  async execute(message: Message): Promise<void> {
    console.log('ahhhhhh');
    try {
      // Skip non-guild messages and bot messages
      if (!message.inGuild() || message.author.bot) {
        return;
      }

      // Check for prefix commands
      const prefix = this.getPrefix(message);
      if (message.content.startsWith(prefix)) {
        await this.handlePrefixCommand(message, prefix);
        return;
      }

      // Handle mention as prefix
      const mentionPrefix = this.getMentionPrefix(message);
      if (mentionPrefix && message.content.startsWith(mentionPrefix)) {
        await this.handlePrefixCommand(message, mentionPrefix);
        return;
      }

      // Handle other message processing (chat messages, calls, etc.)
      // This would integrate with existing message processors
      await this.handleNonCommandMessage(message);

    }
    catch (error) {
      Logger.error('Error in MessageCreateEventHandler:', error);
    }
  }

  /**
   * Handle prefix command execution through the flexible command architecture
   */
  private async handlePrefixCommand(message: Message, prefix: string): Promise<void> {
    try {
      // Parse command from message
      const commandInfo = this.parseCommand(message, prefix);
      console.log(commandInfo);
      if (!commandInfo) {
        return;
      }

      const { commandName, args } = commandInfo;

      // Execute command through the registry's flexible architecture
      await this.commandRegistry.executePrefixCommand(message, commandName, args);

    }
    catch (error) {
      Logger.error('Error executing prefix command:', error);
      // Error handling is now done by CommandRegistry
    }
  }

  /**
   * Parse command name and arguments from message
   */
  private parseCommand(message: Message, prefix: string): { commandName: string; args: string[] } | null {
    const content = message.content.slice(prefix.length).trim();
    if (!content) {
      return null;
    }

    const parts = content.split(/\s+/);
    const commandName = parts[0].toLowerCase();
    const args = parts.slice(1);

    return { commandName, args };
  }

  /**
   * Get the prefix for this guild/message
   */
  private getPrefix(_message: Message): string {
    return process.env.PREFIX || 'ic.';
  }

  /**
   * Get mention prefix if message starts with bot mention
   */
  private getMentionPrefix(message: Message): string | null {
    const botId = message.client.user?.id;
    if (!botId) return null;

    const mentionRegex = new RegExp(`^<@!?${botId}>\\s*`);
    const match = message.content.match(mentionRegex);
    return match ? match[0] : null;
  }

  // Command handling methods removed - now handled by CommandRegistry

  /**
   * Handle non-command messages (chat, calls, etc.)
   */
  private async handleNonCommandMessage(_message: Message): Promise<void> {
    // This would integrate with existing message processors
    // For now, we'll leave this empty as it's outside the scope of command architecture
  }
}
