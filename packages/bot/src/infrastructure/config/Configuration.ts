/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { z } from 'zod';
import { injectable } from 'inversify';

/**
 * Configuration Schema using Zod for runtime validation
 */
const ConfigurationSchema = z.object({
  bot: z.object({
    token: z.string().min(1, 'Bot token is required'),
    clientId: z.string().min(1, 'Client ID is required'),
    devGuildId: z.string().optional(),
    presence: z.object({
      status: z.enum(['online', 'idle', 'dnd', 'invisible']).default('online'),
      activity: z.object({
        name: z.string().default('InterChat'),
        type: z.number().default(0),
      }).default({ name: 'InterChat', type: 0 }),
    }).default({ status: 'online', activity: { name: 'InterChat', type: 0 } }),
  }),

  database: z.object({
    url: z.string().url('Invalid database URL'),
    pool: z.object({
      min: z.number().min(1).default(2),
      max: z.number().min(1).default(10),
      acquireTimeoutMillis: z.number().default(30000),
      idleTimeoutMillis: z.number().default(600000),
    }).default({}),
  }),

  redis: z.object({
    url: z.string().url('Invalid Redis URL'),
    keyPrefix: z.string().default('interchat:'),
    defaultTTL: z.number().default(300), // 5 minutes
  }),

  api: z.object({
    port: z.number().min(1).max(65535).default(3000),
    cors: z.object({
      origin: z.union([z.string(), z.array(z.string())]).default('*'),
      credentials: z.boolean().default(true),
    }).default({}),
    rateLimit: z.object({
      windowMs: z.number().default(15 * 60 * 1000), // 15 minutes
      max: z.number().default(100), // limit each IP to 100 requests per windowMs
    }).default({}),
  }),

  logging: z.object({
    level: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    format: z.enum(['json', 'simple']).default('json'),
    file: z.object({
      enabled: z.boolean().default(true),
      filename: z.string().default('logs/app.log'),
      maxsize: z.number().default(5242880), // 5MB
      maxFiles: z.number().default(5),
    }).default({}),
  }),

  monitoring: z.object({
    enabled: z.boolean().default(false),
    prometheus: z.object({
      port: z.number().min(1).max(65535).default(9090),
      path: z.string().default('/metrics'),
    }).default({}),
    sentry: z.object({
      dsn: z.string().optional(),
      environment: z.string().default('development'),
    }).default({}),
  }),

  features: z.object({
    premium: z.object({
      enabled: z.boolean().default(true),
      kofi: z.object({
        webhookSecret: z.string().optional(),
        supporterTierAmount: z.number().default(3),
      }).default({}),
    }).default({}),
    analytics: z.object({
      enabled: z.boolean().default(false),
    }).default({}),
  }),
});

export type ConfigurationSchema = z.infer<typeof ConfigurationSchema>;

/**
 * Configuration Service
 *
 * Manages application configuration with validation and type safety.
 * Loads configuration from environment variables and validates them
 * against the schema.
 */
@injectable()
export class Configuration {
  private config: ConfigurationSchema;

  constructor() {
    this.config = this.loadAndValidateConfig();
  }

  private loadAndValidateConfig(): ConfigurationSchema {
    const config = {
      bot: {
        token: process.env.BOT_TOKEN,
        clientId: process.env.CLIENT_ID,
        devGuildId: process.env.DEV_GUILD_ID,
        presence: {
          status: process.env.BOT_STATUS as any,
          activity: {
            name: process.env.BOT_ACTIVITY_NAME,
            type: process.env.BOT_ACTIVITY_TYPE ? parseInt(process.env.BOT_ACTIVITY_TYPE) : undefined,
          },
        },
      },

      database: {
        url: process.env.DATABASE_URL,
        pool: {
          min: process.env.DB_POOL_MIN ? parseInt(process.env.DB_POOL_MIN) : undefined,
          max: process.env.DB_POOL_MAX ? parseInt(process.env.DB_POOL_MAX) : undefined,
          acquireTimeoutMillis: process.env.DB_ACQUIRE_TIMEOUT ? parseInt(process.env.DB_ACQUIRE_TIMEOUT) : undefined,
          idleTimeoutMillis: process.env.DB_IDLE_TIMEOUT ? parseInt(process.env.DB_IDLE_TIMEOUT) : undefined,
        },
      },

      redis: {
        url: process.env.REDIS_URL,
        keyPrefix: process.env.REDIS_KEY_PREFIX,
        defaultTTL: process.env.REDIS_DEFAULT_TTL ? parseInt(process.env.REDIS_DEFAULT_TTL) : undefined,
      },

      api: {
        port: process.env.API_PORT ? parseInt(process.env.API_PORT) : undefined,
        cors: {
          origin: process.env.CORS_ORIGIN,
          credentials: process.env.CORS_CREDENTIALS ? process.env.CORS_CREDENTIALS === 'true' : undefined,
        },
        rateLimit: {
          windowMs: process.env.RATE_LIMIT_WINDOW ? parseInt(process.env.RATE_LIMIT_WINDOW) : undefined,
          max: process.env.RATE_LIMIT_MAX ? parseInt(process.env.RATE_LIMIT_MAX) : undefined,
        },
      },

      logging: {
        level: process.env.LOG_LEVEL as any,
        format: process.env.LOG_FORMAT as any,
        file: {
          enabled: process.env.LOG_FILE_ENABLED ? process.env.LOG_FILE_ENABLED === 'true' : undefined,
          filename: process.env.LOG_FILE_NAME,
          maxsize: process.env.LOG_FILE_MAX_SIZE ? parseInt(process.env.LOG_FILE_MAX_SIZE) : undefined,
          maxFiles: process.env.LOG_FILE_MAX_FILES ? parseInt(process.env.LOG_FILE_MAX_FILES) : undefined,
        },
      },

      monitoring: {
        enabled: process.env.MONITORING_ENABLED ? process.env.MONITORING_ENABLED === 'true' : undefined,
        prometheus: {
          port: process.env.PROMETHEUS_PORT ? parseInt(process.env.PROMETHEUS_PORT) : undefined,
          path: process.env.PROMETHEUS_PATH,
        },
        sentry: {
          dsn: process.env.SENTRY_DSN,
          environment: process.env.NODE_ENV,
        },
      },

      features: {
        premium: {
          enabled: process.env.PREMIUM_ENABLED ? process.env.PREMIUM_ENABLED === 'true' : undefined,
          kofi: {
            webhookSecret: process.env.KOFI_WEBHOOK_SECRET,
            supporterTierAmount: process.env.KOFI_SUPPORTER_AMOUNT ? parseInt(process.env.KOFI_SUPPORTER_AMOUNT) : undefined,
          },
        },
        analytics: {
          enabled: process.env.ANALYTICS_ENABLED ? process.env.ANALYTICS_ENABLED === 'true' : undefined,
        },
      },
    };

    try {
      return ConfigurationSchema.parse(config);
    }
    catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
        throw new Error(`Configuration validation failed:\n${errorMessages.join('\n')}`);
      }
      throw error;
    }
  }

  /**
   * Get a configuration value by key path
   */
  get<T extends keyof ConfigurationSchema>(key: T): ConfigurationSchema[T] {
    return this.config[key];
  }

  /**
   * Get the entire configuration object
   */
  getAll(): ConfigurationSchema {
    return { ...this.config };
  }

  /**
   * Check if a feature is enabled
   */
  isFeatureEnabled(feature: string): boolean {
    const features = this.config.features as any;
    return features[feature]?.enabled ?? false;
  }

  /**
   * Get environment name
   */
  getEnvironment(): string {
    return process.env.NODE_ENV || 'development';
  }

  /**
   * Check if running in development mode
   */
  isDevelopment(): boolean {
    return this.getEnvironment() === 'development';
  }

  /**
   * Check if running in production mode
   */
  isProduction(): boolean {
    return this.getEnvironment() === 'production';
  }
}
