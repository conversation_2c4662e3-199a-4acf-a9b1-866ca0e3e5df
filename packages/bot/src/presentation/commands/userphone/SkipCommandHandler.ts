/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Skip Command Handler
 *
 * Handles skipping the current call and finding a new match.
 */

import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  SlashCommandBuilder,
} from 'discord.js';
import { injectable } from 'inversify';
import type { Context } from '../../../shared/context/Context.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';

/**
 * Skip Command Handler
 *
 * Provides functionality to skip the current call and find a new match.
 */
@injectable()
export default class Skip<PERSON>ommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'skip',
    description: '[BETA] Skip the current call and find a new match',
    category: CommandCategory.USERPHONE,
    staffOnly: false,
    ownerOnly: false,
    guildOnly: true, // Must be used in a guild
    cooldown: 5000, // 5 second cooldown to prevent spam
  };

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description);
  }

  async execute(ctx: Context): Promise<CommandResult> {
    try {
      // Check if guild is available
      if (!ctx.inGuild()) {
        const embed = this.createErrorEmbed(
          'Guild Required',
          'This command can only be used in a server.',
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // Check if user has manage channels permission
      const member = ctx.guild.members.cache.get(ctx.user.id);
      if (!member?.permissions.has('ManageChannels')) {
        const embed = this.createErrorEmbed(
          'Permission Required',
          'You need the **Manage Channels** permission to skip a call.',
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // Check if there's an active call in this channel
      // In a real implementation, you'd check your call state management system
      const hasActiveCall = false; // Placeholder

      if (!hasActiveCall) {
        const embed = this.createErrorEmbed(
          'No Active Call',
          'There is no active call in this channel to skip.',
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // Create skip confirmation embed
      const embed = this.createInfoEmbed(
        '⏭️ Skip Call?',
        'Are you sure you want to skip the current call?\n\n' +
        '• The current call will be ended\n' +
        '• You will be matched with a new server\n' +
        '• This action cannot be undone',
      );

      // Create confirmation buttons
      const confirmButton = new ButtonBuilder()
        .setCustomId(`skip_confirm_${ctx.user.id}_${Date.now()}`)
        .setLabel('Skip Call')
        .setStyle(ButtonStyle.Primary)
        .setEmoji('⏭️');

      const cancelButton = new ButtonBuilder()
        .setCustomId(`skip_cancel_${ctx.user.id}_${Date.now()}`)
        .setLabel('Cancel')
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('❌');

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(confirmButton, cancelButton);

      return {
        success: true,
        embed,
        components: [row],
        ephemeral: true,
      };

    }
    catch (error) {
      const embed = this.createErrorEmbed(
        'Skip Failed',
        'An error occurred while trying to skip the call.',
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  /**
   * Execute the actual skip (called by ctx handler)
   */
  async executeSkip(channelId: string, userId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // In a real implementation, you would:
      // 1. End the current call
      // 2. Remove from current connection
      // 3. Add back to the call queue
      // 4. Start looking for a new match
      // 5. Update the channel with new status

      console.log(`Call skipped by user ${userId} in channel ${channelId}`);

      return { success: true };
    }
    catch (error) {
      console.error('Failed to execute skip:', error);
      return { success: false, error: 'An error occurred while skipping the call' };
    }
  }

  /**
   * Create searching embed for after skip
   */
  createSearchingEmbed(): {
    embed: any;
    components: ActionRowBuilder<ButtonBuilder>[];
  } {
    const embed = this.createInfoEmbed(
      '🔄 Finding New Match...',
      'Looking for a new server to connect with...\n\n' +
      '⏱️ This may take a few moments\n' +
      '🔄 You can cancel anytime by clicking the button below',
    );

    // Create cancel button
    const cancelButton = new ButtonBuilder()
      .setCustomId(`call_cancel_${Date.now()}`)
      .setLabel('Cancel Search')
      .setStyle(ButtonStyle.Danger)
      .setEmoji('❌');

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(cancelButton);

    return {
      embed,
      components: [row],
    };
  }
}
