/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Hangup Command Handler
 *
 * Handles ending userphone calls.
 */

import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  SlashCommandBuilder,
} from 'discord.js';
import { injectable } from 'inversify';
import type { Context } from '../../../shared/context/Context.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';

/**
 * Hangup Command Handler
 *
 * Provides functionality to end active userphone calls.
 */
@injectable()
export default class HangupCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hangup',
    description: '📞 End the current call',
    category: CommandCategory.USERPHONE,
    staffOnly: false,
    ownerOnly: false,
    guildOnly: true, // Must be used in a guild
    cooldown: 3000,
  };

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description);
  }

  async execute(ctx: Context): Promise<CommandResult> {
    try {
      // Check if guild is available
      if (!ctx.inGuild()) {
        const embed = this.createErrorEmbed(
          'Guild Required',
          'This command can only be used in a server.',
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // Check if user has manage channels permission
      const member = ctx.guild.members.cache.get(ctx.user.id);
      if (!member?.permissions.has('ManageChannels')) {
        const embed = this.createErrorEmbed(
          'Permission Required',
          'You need the **Manage Channels** permission to end a call.',
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // Check if there's an active call in this channel
      // In a real implementation, you'd check your call state management system
      const hasActiveCall = false; // Placeholder
      const callDuration = '5m 32s'; // Placeholder

      if (!hasActiveCall) {
        const embed = this.createErrorEmbed(
          'No Active Call',
          'There is no active call in this channel to hang up.',
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // Create hangup confirmation embed
      const embed = this.createInfoEmbed(
        '📞 End Call?',
        'Are you sure you want to end the current call?\n\n' +
        `**Duration:** ${callDuration}\n` +
        `**Channel:** ${ctx.channel?.toString()}`,
      );

      // Create confirmation buttons
      const confirmButton = new ButtonBuilder()
        .setCustomId(`hangup_confirm_${ctx.user.id}_${Date.now()}`)
        .setLabel('End Call')
        .setStyle(ButtonStyle.Danger)
        .setEmoji('📞');

      const cancelButton = new ButtonBuilder()
        .setCustomId(`hangup_cancel_${ctx.user.id}_${Date.now()}`)
        .setLabel('Cancel')
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('❌');

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(confirmButton, cancelButton);

      return {
        success: true,
        embed,
        components: [row],
        ephemeral: true,
      };

    }
    catch (error) {
      const embed = this.createErrorEmbed(
        'Hangup Failed',
        'An error occurred while trying to end the call.',
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  /**
   * Execute the actual hangup (called by ctx handler)
   */
  async executeHangup(channelId: string, userId: string): Promise<{
    success: boolean;
    duration?: string;
    error?: string;
  }> {
    try {
      // In a real implementation, you would:
      // 1. Find the active call for this channel
      // 2. Calculate call duration
      // 3. End the call connection
      // 4. Clean up call state
      // 5. Notify both channels
      // 6. Optionally show call rating

      const duration = '5m 32s'; // Placeholder

      console.log(`Call ended by user ${userId} in channel ${channelId}`);

      return { success: true, duration };
    }
    catch (error) {
      console.error('Failed to execute hangup:', error);
      return { success: false, error: 'An error occurred while ending the call' };
    }
  }

  /**
   * Create call rating prompt (called after successful hangup)
   */
  createRatingPrompt(duration: string): {
    embed: any;
    components: ActionRowBuilder<ButtonBuilder>[];
  } {
    const embed = this.createSuccessEmbed(
      '📞 Call Ended',
      'Call ended successfully!\n\n' +
      `**Duration:** ${duration}\n\n` +
      'How was your call experience?',
    );

    // Create rating buttons
    const ratingButtons = [
      new ButtonBuilder()
        .setCustomId('call_rating_1')
        .setLabel('1')
        .setStyle(ButtonStyle.Danger)
        .setEmoji('⭐'),
      new ButtonBuilder()
        .setCustomId('call_rating_2')
        .setLabel('2')
        .setStyle(ButtonStyle.Danger)
        .setEmoji('⭐'),
      new ButtonBuilder()
        .setCustomId('call_rating_3')
        .setLabel('3')
        .setStyle(ButtonStyle.Primary)
        .setEmoji('⭐'),
      new ButtonBuilder()
        .setCustomId('call_rating_4')
        .setLabel('4')
        .setStyle(ButtonStyle.Success)
        .setEmoji('⭐'),
      new ButtonBuilder()
        .setCustomId('call_rating_5')
        .setLabel('5')
        .setStyle(ButtonStyle.Success)
        .setEmoji('⭐'),
    ];

    const ratingRow = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(ratingButtons);

    const skipButton = new ButtonBuilder()
      .setCustomId('call_rating_skip')
      .setLabel('Skip Rating')
      .setStyle(ButtonStyle.Secondary)
      .setEmoji('⏭️');

    const skipRow = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(skipButton);

    return {
      embed,
      components: [ratingRow, skipRow],
    };
  }
}
