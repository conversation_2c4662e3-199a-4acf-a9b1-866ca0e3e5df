/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
  SlashCommandBuilder,
  SlashCommandOptionsOnlyBuilder,
} from 'discord.js';
import { inject, injectable } from 'inversify';
import type { DeleteHubUseCase } from '../../../application/use-cases/hub/DeleteHubUseCase.js';
import type { GetHubUseCase } from '../../../application/use-cases/hub/GetHubUseCase.js';
import type { Context } from '../../../shared/context/index.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';

@injectable()
export default class HubDeleteCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hub-delete',
    description: '🗑️ Delete a hub you own.',
    category: CommandCategory.HUB,
    cooldown: 30, // 30 seconds
    permissions: [],
    ownerOnly: false,
    guildOnly: false,
  };

  constructor(
    @inject(TYPES.GetHubUseCase) private readonly getHubUseCase: GetHubUseCase,
    @inject(TYPES.DeleteHubUseCase) private readonly deleteHubUseCase: DeleteHubUseCase,
  ) {
    super();
  }

  buildCommand(): SlashCommandBuilder | SlashCommandOptionsOnlyBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption((option) =>
        option.setName('hub').setDescription('The name of the hub to delete').setRequired(true),
      );
  }

  async execute(context: Context): Promise<CommandResult> {
    const hubName = context.options.getString('hub', true) ?? undefined;
    const userId = context.user.id;

    try {
      // Get the hub by name
      const result = await this.getHubUseCase.execute({ hubName: hubName || undefined });

      if (!result.success || !result.hub) {
        await context.reply({
          content: '❌ Hub not found.',
          ephemeral: true,
        });
        return { success: false };
      }

      const hub = result.hub;

      // Check if user is the owner
      if (!hub.isOwner(userId)) {
        await context.reply({
          content: '❌ You can only delete hubs that you own.',
          ephemeral: true,
        });
        return { success: false };
      }

      // Check if hub can be deleted (business rules)
      if (!hub.canBeDeleted()) {
        await context.reply({
          content: '❌ This hub cannot be deleted at this time.',
          ephemeral: true,
        });
        return { success: false };
      }

      // Show confirmation dialog
      const confirmEmbed = new EmbedBuilder()
        .setDescription(
          `⚠️ Are you sure you want to delete the hub **${hub.name}**?\n\nThis action cannot be undone!`,
        )
        .setColor('Red');

      const confirmButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
        new ButtonBuilder()
          .setLabel('Confirm Delete')
          .setCustomId(`hub_delete_confirm:${hub.id}:${userId}`)
          .setStyle(ButtonStyle.Danger),
        new ButtonBuilder()
          .setLabel('Cancel')
          .setCustomId(`hub_delete_cancel:${hub.id}:${userId}`)
          .setStyle(ButtonStyle.Secondary),
      );

      await context.reply({
        embeds: [confirmEmbed],
        components: [confirmButtons],
        ephemeral: true,
      });

      return { success: true };
    }
    catch (error) {
      console.error('Error in hub delete command:', error);
      await context.reply({
        content: '❌ An error occurred while processing your request.',
        ephemeral: true,
      });
      return { success: false };
    }
  }
}
