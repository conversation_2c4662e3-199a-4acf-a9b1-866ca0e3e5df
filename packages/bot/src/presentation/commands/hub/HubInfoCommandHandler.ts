/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { EmbedBuilder, SlashCommandBuilder, SlashCommandOptionsOnlyBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import type { GetHubUseCase } from '../../../application/use-cases/hub/GetHubUseCase.js';
import type { Hub } from '../../../domain/entities/Hub.js';
import type { Context } from '../../../shared/context/index.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { Logger } from '../../../shared/utils/Logger.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';

@injectable()
export default class HubInfoCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hub-info',
    description: '📊 Show detailed information about a hub',
    category: CommandCategory.HUB,
    cooldown: 5, // 5 second cooldown
    permissions: [],
    guildOnly: false,
    ownerOnly: false,
  };

  constructor(
    @inject(TYPES.GetHubUseCase) private getHubUseCase: GetHubUseCase,
  ) {
    super();

    // Validate that the dependency was injected properly
    if (!this.getHubUseCase) {
      throw new Error('GetHubUseCase dependency was not injected properly');
    }
  }

  buildCommand(): SlashCommandBuilder | SlashCommandOptionsOnlyBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption((option) =>
        option
          .setName('name')
          .setDescription('The name of the hub to get information about')
          .setRequired(true),
      );
  }

  async execute(ctx: Context): Promise<CommandResult> {
    const hubName = this.validateInput(ctx);
    if (!hubName) {
      return this.createErrorResult('❌ **Hub name is required.**');
    }

    // Defer the reply since this operation might take a moment
    await ctx.deferReply({ flags: ['Ephemeral'] });

    const hub = await this.fetchHub(hubName, ctx.user.id);
    if (!hub) {
      return this.createErrorResult('❌ **Hub not found or you don\'t have access to it.**');
    }

    const embed = this.createHubInfoEmbed(hub, ctx.user.id);
    return {
      success: true,
      embed,
      ephemeral: true,
    };
  }

  /**
   * Validate input parameters
   */
  private validateInput(ctx: Context): string | null {
    return ctx.options.getString('name', true);
  }

  /**
   * Create a standardized error result
   */
  private createErrorResult(message: string): CommandResult {
    return {
      success: false,
      message,
      ephemeral: true,
    };
  }

  /**
   * Fetch hub information with proper error handling
   */
  private async fetchHub(hubName: string, userId: string): Promise<Hub | null> {
    // Validate that the use case is available
    if (!this.getHubUseCase || typeof this.getHubUseCase.execute !== 'function') {
      return null;
    }

    try {
      const result = await this.getHubUseCase.execute({
        hubName,
        userId,
      });

      return result.success && result.hub ? result.hub : null;
    }
    catch (error) {
      Logger.error('Error executing GetHubUseCase:', error);
      return null;
    }
  }

  /**
   * Create the hub information embed
   */
  private createHubInfoEmbed(hub: Hub, userId: string): EmbedBuilder {
    const embed = new EmbedBuilder()
      .setTitle(`📊 ${hub.name}`)
      .setDescription(hub.description)
      .setColor(hub.isPrivate ? 0xff9500 : 0x0099ff);

    // Add icon if available
    if (hub.iconUrl) {
      embed.setThumbnail(hub.iconUrl);
    }

    // Add banner if available
    if (hub.bannerUrl) {
      embed.setImage(hub.bannerUrl);
    }

    this.addBasicFields(embed, hub);
    this.addOptionalFields(embed, hub, userId);
    this.addOwnerFields(embed, hub, userId);
    this.setEmbedFooter(embed, hub, userId);

    return embed;
  }

  /**
   * Add basic hub information fields
   */
  private addBasicFields(embed: EmbedBuilder, hub: Hub): void {
    embed.addFields(
      {
        name: '👑 Owner',
        value: `<@${hub.ownerId}>`,
        inline: true,
      },
      {
        name: '🔒 Privacy',
        value: hub.isPrivate ? 'Private' : 'Public',
        inline: true,
      },
      {
        name: '🔞 NSFW',
        value: hub.isNsfw ? 'Yes' : 'No',
        inline: true,
      },
      {
        name: '📅 Created',
        value: `<t:${Math.floor(hub.createdAt.getTime() / 1000)}:F>`,
        inline: true,
      },
      {
        name: '🕐 Last Active',
        value: `<t:${Math.floor(hub.lastActive.getTime() / 1000)}:R>`,
        inline: true,
      },
      {
        name: '⏰ Appeal Cooldown',
        value: `${hub.appealCooldownHours} hours`,
        inline: true,
      },
    );
  }

  /**
   * Add optional fields (welcome message, rules) based on access permissions
   */
  private addOptionalFields(embed: EmbedBuilder, hub: Hub, userId: string): void {
    const isOwner = hub.isOwner(userId);

    // Add welcome message if available and user has access
    if (hub.welcomeMessage && (isOwner || !hub.isPrivate)) {
      embed.addFields({
        name: '💬 Welcome Message',
        value: hub.welcomeMessage.length > 500
          ? `${hub.welcomeMessage.substring(0, 497)}...`
          : hub.welcomeMessage,
        inline: false,
      });
    }

    // Add rules if available and user has access
    if (hub.rules && hub.rules.length > 0 && (isOwner || !hub.isPrivate)) {
      const rulesText = hub.rules
        .slice(0, 5) // Show only first 5 rules
        .map((rule: string, index: number) => `${index + 1}. ${rule}`)
        .join('\n');

      embed.addFields({
        name: `📋 Rules ${hub.rules.length > 5 ? `(showing 1-5 of ${hub.rules.length})` : ''}`,
        value: rulesText.length > 500
          ? `${rulesText.substring(0, 497)}...`
          : rulesText,
        inline: false,
      });
    }
  }

  /**
   * Add owner-only fields
   */
  private addOwnerFields(embed: EmbedBuilder, hub: Hub, userId: string): void {
    const isOwner = hub.isOwner(userId);

    if (isOwner) {
      embed.addFields({
        name: '🔧 Owner Settings',
        value: `Locked: ${hub.isLocked ? 'Yes' : 'No'}\n` +
                 `Settings: \`${hub.settings || 'Default'}\``,
        inline: false,
      });
    }
  }

  /**
   * Set the embed footer based on user permissions
   */
  private setEmbedFooter(embed: EmbedBuilder, hub: Hub, userId: string): void {
    const isOwner = hub.isOwner(userId);

    embed.setFooter({
      text: isOwner
        ? 'You own this hub'
        : hub.isPrivate
          ? 'This is a private hub'
          : 'This is a public hub',
    });
  }
}
